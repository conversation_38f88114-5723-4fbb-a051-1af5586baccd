// Input Types
export interface CreateProductServiceInput {
  name?: string; // Optional - will be auto-generated if not provided
  type: "Product" | "Service";
  description?: string;
  base_cost?: number; // Base cost in CHF
  custom_fields?: Record<string, any>; // JSON object for dynamic field values
  status?: "active" | "inactive";
  category_id: string;
  unit_type_id: string;
  tag_ids?: string[];
  service_level?: "hotel" | "destination";
  hotel_id?: string; // JSON array of hotel IDs for hotel-level services
  destination_id?: string; // JSON array of destination IDs for destination-level services
}

export interface UpdateProductServiceInput {
  name?: string;
  type?: "Product" | "Service";
  description?: string;
  base_cost?: number; // Base cost in CHF
  custom_fields?: Record<string, any>; // JSON object for dynamic field values
  status?: "active" | "inactive";
  category_id?: string;
  unit_type_id?: string;
  tag_ids?: string[];
  service_level?: "hotel" | "destination";
  hotel_id?: string; // JSON array of hotel IDs for hotel-level services
  destination_id?: string; // JSON array of destination IDs for destination-level services

  // Price Tracking Fields for Flagging System
  highest_price?: number | null; // Highest price seen from supplier offerings
  highest_price_currency?: string | null; // Currency of the highest price
  price_flag_active?: boolean; // Whether a price flag is currently active
  price_flag_created_at?: Date | null; // When the current price flag was created
  price_flag_supplier_offering_id?: string | null; // ID of supplier offering that triggered the flag

  // Cost History Fields
  change_reason?: string; // Optional reason for cost changes
  updated_by?: string; // User who made the update
}

// Dynamic Field Schema Types
export interface DynamicFieldSchema {
  label: string;
  key: string;
  type:
    | "text"
    | "number"
    | "dropdown"
    | "multi-select"
    | "date"
    | "boolean"
    | "number-range"
    | "hotels"
    | "destinations"
    | "addons";
  options?: string[];
  required: boolean;
  used_in_filtering?: boolean;
  used_in_supplier_offering?: boolean;
  used_in_product?: boolean;
  locked_in_offerings?: boolean;
  order?: number; // Optional field ordering for product name generation
  field_context?: "supplier" | "customer"; // Context filtering for fields
}

export interface CreateCategoryInput {
  name: string;
  description?: string;
  category_type?: "Product" | "Service" | "Both";
  icon?: string;
  dynamic_field_schema?: DynamicFieldSchema[];
  is_active?: boolean;
}

export interface UpdateCategoryInput {
  name?: string;
  description?: string;
  category_type?: "Product" | "Service" | "Both";
  icon?: string;
  dynamic_field_schema?: DynamicFieldSchema[];
  is_active?: boolean;
}

export interface CreateUnitTypeInput {
  name: string;
  description?: string;
  is_active?: boolean;
}

export interface UpdateUnitTypeInput {
  name?: string;
  description?: string;
  is_active?: boolean;
}

export interface CreateTagInput {
  name: string;
  color?: string;
  is_active?: boolean;
}

export interface UpdateTagInput {
  name?: string;
  color?: string;
  is_active?: boolean;
}

export interface CreateCustomFieldInput {
  name: string;
  field_type: "dropdown" | "text" | "number" | "date" | "boolean";
  options?: string[];
  is_required?: boolean;
  default_value?: string;
  validation_rules?: Record<string, any>;
  field_context?: "supplier" | "customer";
  is_active?: boolean;
}

export interface UpdateCustomFieldInput {
  name?: string;
  field_type?: "dropdown" | "text" | "number" | "date" | "boolean";
  options?: string[];
  is_required?: boolean;
  default_value?: string;
  validation_rules?: Record<string, any>;
  field_context?: "supplier" | "customer";
  is_active?: boolean;
}

// ProductServiceSupplier Types
export interface CreateProductServiceSupplierInput {
  product_service_id: string;
  supplier_id: string;
  cost: number;
  currency_code?: string;
  availability: string;
  max_capacity?: number;
  season?: string;
  valid_from?: Date;
  valid_until?: Date;
  notes?: string;
  lead_time_days?: number;
  minimum_order?: number;
  is_active?: boolean;
  is_preferred?: boolean;
}

export interface UpdateProductServiceSupplierInput {
  cost?: number;
  currency_code?: string;
  availability?: string;
  max_capacity?: number;
  season?: string;
  valid_from?: Date;
  valid_until?: Date;
  notes?: string;
  lead_time_days?: number;
  minimum_order?: number;
  is_active?: boolean;
  is_preferred?: boolean;
}

// Output Types
export interface ProductServiceOutput {
  id: string;
  name: string;
  type: "Product" | "Service";
  description?: string;
  base_cost?: number; // Base cost in CHF
  custom_fields?: Record<string, any>; // JSON object for dynamic field values
  status: "active" | "inactive";
  category_id: string;
  unit_type_id: string;
  service_level: "hotel" | "destination";
  hotel_id?: string; // JSON array of hotel IDs for hotel-level services
  destination_id?: string; // JSON array of destination IDs for destination-level services
  category?: CategoryOutput;
  unit_type?: UnitTypeOutput;
  tags?: TagOutput[];
  suppliers?: ProductServiceSupplierOutput[];
  created_at: Date;
  updated_at: Date;
}

export interface CategoryOutput {
  id: string;
  name: string;
  description?: string;
  category_type: "Product" | "Service" | "Both";
  icon?: string;
  dynamic_field_schema?: DynamicFieldSchema[];
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface UnitTypeOutput {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface TagOutput {
  id: string;
  name: string;
  color?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface CustomFieldOutput {
  id: string;
  name: string;
  field_type: "dropdown" | "text" | "number" | "date" | "boolean";
  options?: string[];
  is_required: boolean;
  default_value?: string;
  validation_rules?: Record<string, any>;
  field_context: "supplier" | "customer";
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface ProductServiceSupplierOutput {
  id: string;
  product_service_id: string;
  supplier_id: string;
  cost: number;
  currency_code: string;
  availability: string;
  max_capacity?: number;
  season?: string;
  valid_from?: Date;
  valid_until?: Date;
  notes?: string;
  lead_time_days?: number;
  minimum_order?: number;
  is_active: boolean;
  is_preferred: boolean;
  created_by?: string;
  updated_by?: string;
  created_at: Date;
  updated_at: Date;
  // Populated supplier information
  supplier?: {
    id: string;
    name: string;
    type: string;
    status: string;
    primary_contact_name?: string;
    primary_contact_email?: string;
  };
}

// Filter Types
export interface ProductServiceFilters {
  name?: string;
  type?: "Product" | "Service";
  status?: "active" | "inactive";
  category_id?: string;
  unit_type_id?: string;
  tag_ids?: string[];
  service_level?: "hotel" | "destination";
  hotel_id?: string;
  destination_id?: string;
}

export interface CategoryFilters {
  name?: string;
  category_type?: "Product" | "Service" | "Both";
  is_active?: boolean;
}

export interface UnitTypeFilters {
  name?: string;
  is_active?: boolean;
}

export interface TagFilters {
  name?: string;
  is_active?: boolean;
}

export interface CustomFieldFilters {
  name?: string;
  field_type?: "dropdown" | "text" | "number" | "date" | "boolean";
  is_active?: boolean;
}

export interface ProductServiceSupplierFilters {
  product_service_id?: string;
  supplier_id?: string;
  is_active?: boolean;
  is_preferred?: boolean;
  season?: string;
  limit?: number;
  offset?: number;
}

// List Response Types
export interface ProductServiceListResponse {
  data: ProductServiceOutput[];
  count: number;
  limit: number;
  offset: number;
}

export interface CategoryListResponse {
  data: CategoryOutput[];
  count: number;
  limit: number;
  offset: number;
}

export interface UnitTypeListResponse {
  data: UnitTypeOutput[];
  count: number;
  limit: number;
  offset: number;
}

export interface TagListResponse {
  data: TagOutput[];
  count: number;
  limit: number;
  offset: number;
}

export interface CustomFieldListResponse {
  data: CustomFieldOutput[];
  count: number;
  limit: number;
  offset: number;
}

export interface ProductServiceSupplierListResponse {
  data: ProductServiceSupplierOutput[];
  count: number;
  limit: number;
  offset: number;
}

// Supplier Offering Types
export interface CreateSupplierOfferingInput {
  product_service_id: string;
  supplier_id: string;
  active_from?: Date;
  active_to?: Date;
  availability_notes?: string;

  // Legacy cost field (for backward compatibility)
  cost?: number;

  // Enhanced pricing fields
  commission?: number; // Commission percentage (0.1 for 10%)
  gross_price?: number; // Gross price (renamed from public_price)
  net_cost?: number; // Net cost to supplier: Gross Price - (Gross Price × Commission)
  margin_rate?: number; // Margin rate percentage (0.1 for 10%)
  selling_price?: number; // Calculated: supplier_price ÷ (1 - Margin Rate)

  // Currency fields
  currency?: string; // Cost currency
  currency_override?: boolean;

  // Selling currency fields
  selling_currency?: string; // Selling currency (can be different from cost currency)
  selling_price_selling_currency?: number; // Selling price in selling currency
  exchange_rate?: number; // Exchange rate from cost currency to selling currency
  exchange_rate_date?: Date; // When the exchange rate was last updated

  status?: "active" | "inactive";
  custom_fields?: Record<string, any>; // JSON object for category-specific dynamic field values

  // Add-ons Configuration (JSON storage for addon line items with mandatory/optional settings)
  add_ons?: any[]; // JSON storage for addon line items with pricing and mandatory flags

  created_by?: string;
}

export interface UpdateSupplierOfferingInput {
  product_service_id?: string;
  supplier_id?: string;
  active_from?: Date;
  active_to?: Date;
  availability_notes?: string;
  supplier_price?: number;

  // Legacy cost field (for backward compatibility)
  cost?: number;

  // Enhanced pricing fields
  commission?: number; // Commission percentage (0.1 for 10%)
  gross_price?: number; // Gross price for this offering
  net_cost?: number; // Net cost to supplier: Gross Price - (Gross Price × Commission)
  net_price?: number; // Calculated: Supplier Price + sum of custom prices
  margin_rate?: number; // Margin rate percentage (0.1 for 10%)
  selling_price?: number; // Calculated: Net Price ÷ (1 - Margin Rate)
  custom_prices?: Array<{ name: string; price: number }>; // Custom additional prices

  // Currency fields
  currency?: string; // Cost currency
  currency_override?: boolean;

  // Selling currency fields
  selling_currency?: string; // Selling currency (can be different from cost currency)
  selling_price_selling_currency?: number; // Selling price in selling currency
  exchange_rate?: number; // Exchange rate from cost currency to selling currency
  exchange_rate_date?: Date; // When the exchange rate was last updated

  status?: "active" | "inactive";
  custom_fields?: Record<string, any>; // JSON object for category-specific dynamic field values

  // Add-ons Configuration (JSON storage for addon line items with mandatory/optional settings)
  add_ons?: any[]; // JSON storage for addon line items with pricing and mandatory flags

  updated_by?: string;
  change_reason?: string; // Optional reason for pricing changes
}

export interface SupplierOfferingOutput {
  id: string;
  product_service_id: string;
  supplier_id: string;
  active_from?: Date;
  active_to?: Date;
  availability_notes?: string;

  // Legacy cost field (for backward compatibility)
  cost?: number;

  // Enhanced pricing fields
  commission?: number; // Commission percentage (0.1 for 10%)
  gross_price?: number; // Gross price for this offering
  net_cost?: number; // Net cost to supplier: Gross Price - (Gross Price × Commission)
  net_price?: number; // Calculated: Supplier Price + sum of custom prices
  margin_rate?: number; // Margin rate percentage (0.1 for 10%)
  selling_price?: number; // Calculated: Net Price ÷ (1 - Margin Rate)
  custom_prices?: Array<{ name: string; price: number }>; // Custom additional prices

  // Calculated fields (computed on the fly)
  calculated_supplier_price?: number; // Real-time calculation
  calculated_net_price?: number; // Real-time calculation
  calculated_selling_price?: number; // Real-time calculation
  is_pricing_complete?: boolean; // Whether all required pricing fields are set
  pricing_errors?: string[]; // Validation errors in pricing

  // Currency fields
  currency?: string; // Cost currency
  currency_override?: boolean;

  // Selling currency fields
  selling_currency?: string; // Selling currency (can be different from cost currency)
  selling_price_selling_currency?: number; // Selling price in selling currency
  exchange_rate?: number; // Exchange rate from cost currency to selling currency
  exchange_rate_date?: Date; // When the exchange rate was last updated

  // Calculated selling currency fields
  calculated_selling_price_selling_currency?: number; // Real-time calculation in selling currency

  status: "active" | "inactive";
  custom_fields?: Record<string, any>; // JSON object for category-specific dynamic field values

  // Add-ons Configuration (JSON storage for addon line items with mandatory/optional settings)
  add_ons?: any[]; // JSON storage for addon line items with pricing and mandatory flags

  created_by?: string;
  updated_by?: string;
  created_at: Date;
  updated_at: Date;
  // Populated relationships
  product_service?: ProductServiceOutput;
  supplier?: {
    id: string;
    name: string;
    type: string;
    status: string;
    primary_contact_name?: string;
    primary_contact_email?: string;
    default_currency?: string;
  };
}

export interface SupplierOfferingFilters {
  id?: string;
  supplier_id?: string;
  product_service_id?: string;
  category_id?: string; // Filter by product/service category
  status?: "active" | "inactive";
  active_from?: Date;
  active_to?: Date;
  created_by?: string;
  updated_by?: string;
  search?: string; // Search in product/service name, supplier name, category name
}

export interface SupplierOfferingListResponse {
  data: SupplierOfferingOutput[];
  count: number;
  limit: number;
  offset: number;
}

// Supplier Offering Cost History Types
export interface CreateSupplierOfferingCostHistoryInput {
  supplier_offering_id: string;
  previous_cost?: number;
  new_cost?: number;
  previous_currency?: string;
  new_currency?: string;
  change_reason?: string;
  changed_by_user_id?: string;
}

export interface UpdateSupplierOfferingCostHistoryInput {
  change_reason?: string;
  changed_by_user_id?: string;
}

export interface SupplierOfferingCostHistoryOutput {
  id: string;
  supplier_offering_id: string;
  previous_cost?: number;
  new_cost?: number;
  previous_currency?: string;
  new_currency?: string;
  change_reason?: string;
  changed_by_user_id?: string;
  created_at: Date;
  updated_at: Date;
  // Populated relationships
  supplier_offering?: SupplierOfferingOutput;
}

export interface SupplierOfferingCostHistoryFilters {
  id?: string;
  supplier_offering_id?: string;
  changed_by_user_id?: string;
  date_from?: Date;
  date_to?: Date;
  has_cost_change?: boolean;
  has_currency_change?: boolean;
  limit?: number;
  offset?: number;
}

export interface SupplierOfferingCostHistoryListResponse {
  data: SupplierOfferingCostHistoryOutput[];
  count: number;
  limit: number;
  offset: number;
}

export interface SupplierOfferingCostHistoryStats {
  total_changes: number;
  cost_increases: number;
  cost_decreases: number;
  currency_changes: number;
  average_cost_change_percentage?: number;
  most_recent_change?: SupplierOfferingCostHistoryOutput;
  most_frequent_changer?: {
    user_id: string;
    change_count: number;
  };
}

// Product Service Cost History Types
export interface CreateProductServiceCostHistoryInput {
  product_service_id: string;
  previous_cost?: number;
  new_cost?: number;
  change_reason?: string;
  changed_by_user_id?: string;
}

export interface UpdateProductServiceCostHistoryInput {
  change_reason?: string;
  changed_by_user_id?: string;
}

export interface ProductServiceCostHistoryOutput {
  id: string;
  product_service_id: string;
  previous_cost?: number;
  new_cost?: number;
  change_reason?: string;
  changed_by_user_id?: string;
  created_at: Date;
  updated_at: Date;
  // Populated relationships
  product_service?: ProductServiceOutput;
}

export interface ProductServiceCostHistoryFilters {
  id?: string;
  product_service_id?: string;
  changed_by_user_id?: string;
  date_from?: Date;
  date_to?: Date;
  has_cost_change?: boolean;
  limit?: number;
  offset?: number;
}

export interface ProductServiceCostHistoryListResponse {
  data: ProductServiceCostHistoryOutput[];
  count: number;
  limit: number;
  offset: number;
}

export interface ProductServiceCostHistoryStats {
  total_changes: number;
  cost_increases: number;
  cost_decreases: number;
  average_cost_change_percentage?: number;
  most_recent_change?: ProductServiceCostHistoryOutput;
  most_frequent_changer?: {
    user_id: string;
    change_count: number;
  };
}

// Enhanced Supplier Offering Pricing History Types
export interface CreateSupplierOfferingPricingHistoryInput {
  supplier_offering_id: string;

  // Previous values
  previous_commission?: number;
  previous_public_price?: number;
  previous_supplier_price?: number;
  previous_net_price?: number;
  previous_margin_rate?: number;
  previous_selling_price?: number;
  previous_custom_prices?: Array<{ name: string; price: number }>;
  previous_currency?: string;

  // New values
  new_commission?: number;
  new_public_price?: number;
  new_supplier_price?: number;
  new_net_price?: number;
  new_margin_rate?: number;
  new_selling_price?: number;
  new_custom_prices?: Array<{ name: string; price: number }>;
  new_currency?: string;

  change_type:
    | "commission_update"
    | "public_price_update"
    | "margin_rate_update"
    | "custom_prices_update"
    | "currency_update"
    | "bulk_pricing_update"
    | "initial_creation"
    | "legacy_cost_migration";
  change_reason?: string;
  changed_by_user_id?: string;
}

export interface UpdateSupplierOfferingPricingHistoryInput {
  change_reason?: string;
  changed_by_user_id?: string;
}

export interface SupplierOfferingPricingHistoryOutput {
  id: string;
  supplier_offering_id: string;

  // Previous values
  previous_commission?: number;
  previous_public_price?: number;
  previous_supplier_price?: number;
  previous_net_price?: number;
  previous_margin_rate?: number;
  previous_selling_price?: number;
  previous_custom_prices?: Array<{ name: string; price: number }>;
  previous_currency?: string;

  // New values
  new_commission?: number;
  new_public_price?: number;
  new_supplier_price?: number;
  new_net_price?: number;
  new_margin_rate?: number;
  new_selling_price?: number;
  new_custom_prices?: Array<{ name: string; price: number }>;
  new_currency?: string;

  change_type:
    | "commission_update"
    | "public_price_update"
    | "margin_rate_update"
    | "custom_prices_update"
    | "currency_update"
    | "bulk_pricing_update"
    | "initial_creation"
    | "legacy_cost_migration";
  change_reason?: string;
  changed_by_user_id?: string;

  // Calculated change metrics
  commission_change_percent?: number;
  public_price_change_percent?: number;
  margin_rate_change_percent?: number;
  selling_price_change_percent?: number;
  selling_price_change_amount?: number;

  created_at: Date;
  updated_at: Date;

  // Populated relationships
  supplier_offering?: SupplierOfferingOutput;
}

export interface SupplierOfferingPricingHistoryFilters {
  id?: string;
  supplier_offering_id?: string;
  change_type?: string;
  changed_by_user_id?: string;
  date_from?: Date;
  date_to?: Date;
  has_commission_change?: boolean;
  has_public_price_change?: boolean;
  has_margin_rate_change?: boolean;
  has_custom_prices_change?: boolean;
  limit?: number;
  offset?: number;
}

export interface SupplierOfferingPricingHistoryListResponse {
  data: SupplierOfferingPricingHistoryOutput[];
  count: number;
  limit: number;
  offset: number;
}

export interface SupplierOfferingPricingHistoryStats {
  total_changes: number;
  commission_changes: number;
  public_price_changes: number;
  margin_rate_changes: number;
  custom_prices_changes: number;
  currency_changes: number;
  average_selling_price_change_percentage?: number;
  most_recent_change?: SupplierOfferingPricingHistoryOutput;
  most_frequent_changer?: {
    user_id: string;
    change_count: number;
  };
}
