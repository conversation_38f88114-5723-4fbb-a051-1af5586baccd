import React, { useState, useEffect, useRef } from "react";
import { Input, Text, Select } from "@camped-ai/ui";
import { Calculator, Lock } from "lucide-react";
import { AddonLineItem } from "./addon-line-item";

interface PricingData {
  commission?: number;
  grossPrice?: number; // Gross price
  supplierPrice?: number; // Net cost to supplier
  marginRate?: number;
  sellingPrice?: number;

  // Currency fields
  currency?: string; // Cost currency

  // Selling currency fields
  sellingCurrency?: string;
  sellingPriceSellingCurrency?: number;
  exchangeRate?: number;
  exchangeRateDate?: Date;

  // Addon line items
  addonLineItems?: AddonLineItem[];
}

interface PricingCalculatorProps {
  initialData?: PricingData;
  onChange?: (
    data: PricingData & {
      calculatedSupplierPrice?: number;
      calculatedNetPrice?: number;
      calculatedSellingPrice?: number;
      calculatedSellingPriceSellingCurrency?: number;
      calculatedTotalWithAddons?: number;
      calculatedTotalWithAddonsSellingCurrency?: number;
      addonsTotalPrice?: number;
      isPricingComplete?: boolean;
      pricingErrors?: string[];
    }
  ) => void;
  disabled?: boolean;
  costCurrency?: string; // The cost currency (e.g., CHF)
  onAddonSelectionChange?: (addonIds: string[]) => void; // Callback for when addons are selected from custom fields
}

const PricingCalculator: React.FC<PricingCalculatorProps> = ({
  initialData = {},
  onChange,
  disabled = false,
  costCurrency = "CHF",
}) => {
  const [pricingData, setPricingData] = useState<PricingData>({});
  const [errors, setErrors] = useState<string[]>([]);
  const [addonLineItems, setAddonLineItems] = useState<AddonLineItem[]>([]);
  const initializedRef = useRef(false);

  // Initialize data only once when initialData has meaningful values
  useEffect(() => {
    // Check if initialData has any meaningful values and we haven't initialized yet
    const hasData =
      initialData.commission !== undefined ||
      initialData.grossPrice !== undefined ||
      initialData.supplierPrice !== undefined ||
      initialData.marginRate !== undefined ||
      initialData.sellingPrice !== undefined ||
      (initialData.addonLineItems && initialData.addonLineItems.length > 0);

    if (hasData && !initializedRef.current) {
      setPricingData({
        commission: initialData.commission,
        grossPrice: initialData.grossPrice,
        supplierPrice: initialData.supplierPrice,
        marginRate: initialData.marginRate,
        sellingPrice: initialData.sellingPrice,
        currency: initialData.currency,
        sellingCurrency: initialData.sellingCurrency,
        sellingPriceSellingCurrency: initialData.sellingPriceSellingCurrency,
        exchangeRate: initialData.exchangeRate,
        exchangeRateDate: initialData.exchangeRateDate,
      });

      if (initialData.addonLineItems) {
        setAddonLineItems(initialData.addonLineItems);
      }

      initializedRef.current = true;
    }
  }, [initialData]);

  // Separate effect to handle addon line items updates after initialization
  useEffect(() => {
    if (initializedRef.current && initialData.addonLineItems) {
      console.log(
        "🔍 PricingCalculator: Updating addon line items after initialization:",
        initialData.addonLineItems
      );
      setAddonLineItems(initialData.addonLineItems);
    }
  }, [initialData.addonLineItems]);

  // Calculate derived values
  const calculatePricing = (data: PricingData) => {
    const calculationErrors: string[] = [];
    let calculatedSupplierPrice: number | undefined;
    let calculatedSellingPrice: number | undefined;

    try {
      // Ensure numeric values
      const commission =
        typeof data.commission === "string"
          ? parseFloat(data.commission)
          : data.commission;
      const grossPrice =
        typeof data.grossPrice === "string"
          ? parseFloat(data.grossPrice)
          : data.grossPrice;
      const supplierPrice =
        typeof data.supplierPrice === "string"
          ? parseFloat(data.supplierPrice)
          : data.supplierPrice;
      const marginRate =
        typeof data.marginRate === "string"
          ? parseFloat(data.marginRate)
          : data.marginRate;

      // Calculate difference value: Gross Price × Commission (if both provided)
      let differenceValue: number | undefined;
      if (
        commission !== undefined &&
        grossPrice !== undefined &&
        !isNaN(commission) &&
        !isNaN(grossPrice)
      ) {
        if (commission < 0 || commission > 1) {
          calculationErrors.push(
            "Commission must be between 0 and 1 (0% to 100%)"
          );
        } else if (grossPrice < 0) {
          calculationErrors.push("Gross price must be non-negative");
        } else {
          differenceValue = grossPrice * commission;
          // Calculate Net Cost: Gross Price - Difference Value
          calculatedSupplierPrice = grossPrice - differenceValue;
        }
      }

      // Use calculated or manually entered net cost
      const finalSupplierPrice =
        calculatedSupplierPrice ||
        (supplierPrice && !isNaN(supplierPrice) ? supplierPrice : undefined);

      // Calculate selling price: Net Cost ÷ (1 - Margin Rate)
      if (
        finalSupplierPrice &&
        marginRate !== undefined &&
        !isNaN(marginRate)
      ) {
        if (marginRate < 0 || marginRate >= 1) {
          calculationErrors.push(
            "Margin rate must be between 0 and 1 (0% to 99.99%)"
          );
        } else {
          calculatedSellingPrice = finalSupplierPrice / (1 - marginRate);
        }
      }
    } catch (error) {
      calculationErrors.push("Error in pricing calculations");
    }

    // Calculate selling price in selling currency
    let calculatedSellingPriceSellingCurrency: number | undefined;
    if (calculatedSellingPrice && data.sellingCurrency && data.exchangeRate) {
      const exchangeRate =
        typeof data.exchangeRate === "string"
          ? parseFloat(data.exchangeRate)
          : data.exchangeRate;
      if (data.sellingCurrency === costCurrency) {
        // Same currency, no conversion needed
        calculatedSellingPriceSellingCurrency = calculatedSellingPrice;
      } else if (!isNaN(exchangeRate)) {
        // Convert using exchange rate
        calculatedSellingPriceSellingCurrency =
          calculatedSellingPrice * exchangeRate;
      }
    }

    // Pricing is complete if we have either:
    // 1. Commission + Gross Price (calculated net cost), OR
    // 2. Direct net cost entry
    // Plus margin rate and selling price
    // Calculate addon totals
    const addonsTotalPrice = addonLineItems.reduce((sum, item) => {
      return sum + (item.selling_price || 0);
    }, 0);

    // Calculate total with addons
    let calculatedTotalWithAddons: number | undefined;
    let calculatedTotalWithAddonsSellingCurrency: number | undefined;

    if (calculatedSellingPrice !== undefined) {
      calculatedTotalWithAddons = calculatedSellingPrice + addonsTotalPrice;

      // Convert total to selling currency if needed
      if (calculatedSellingPriceSellingCurrency !== undefined) {
        const exchangeRate =
          typeof data.exchangeRate === "string"
            ? parseFloat(data.exchangeRate)
            : data.exchangeRate;

        if (data.sellingCurrency === costCurrency) {
          calculatedTotalWithAddonsSellingCurrency = calculatedTotalWithAddons;
        } else if (exchangeRate && !isNaN(exchangeRate)) {
          calculatedTotalWithAddonsSellingCurrency =
            calculatedTotalWithAddons * exchangeRate;
        }
      }
    }

    const hasNetCost = !!(calculatedSupplierPrice || data.supplierPrice);
    const isPricingComplete = !!(
      hasNetCost &&
      data.marginRate !== undefined &&
      calculatedSellingPrice
    );

    return {
      calculatedSupplierPrice,
      calculatedSellingPrice,
      calculatedSellingPriceSellingCurrency,
      calculatedTotalWithAddons,
      calculatedTotalWithAddonsSellingCurrency,
      addonsTotalPrice,
      isPricingComplete,
      pricingErrors: calculationErrors,
    };
  };

  // Update calculations when data changes
  useEffect(() => {
    const calculations = calculatePricing(pricingData);
    setErrors(calculations.pricingErrors);

    if (onChange) {
      onChange({
        ...pricingData,
        addonLineItems,
        ...calculations,
      });
    }
  }, [pricingData, addonLineItems, onChange]);

  const handleInputChange = (
    field: keyof PricingData,
    value: number | undefined
  ) => {
    setPricingData((prev) => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // If commission is being set and we have gross price, clear manual supplier price
      if (
        field === "commission" &&
        value !== undefined &&
        prev.grossPrice !== undefined
      ) {
        newData.supplierPrice = undefined;
      }

      // If gross price is being set and we have commission, clear manual supplier price
      if (
        field === "grossPrice" &&
        value !== undefined &&
        prev.commission !== undefined
      ) {
        newData.supplierPrice = undefined;
      }

      return newData;
    });
  };

  // Handle addon input changes
  const handleAddonInputChange = (
    addonId: string,
    field: keyof AddonLineItem,
    value: any
  ) => {
    setAddonLineItems((prev) =>
      prev.map((addon) =>
        addon.id === addonId ? { ...addon, [field]: value } : addon
      )
    );
  };

  const calculations = calculatePricing(pricingData);

  // Debug logging (removed to prevent infinite calls)

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Calculator className="h-5 w-5" />
        <Text weight="plus" size="large">
          Pricing Calculator
        </Text>
      </div>

      {/* Basic Pricing Fields - Horizontal Table Format */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-ui-border-base">
          <thead>
            <tr className="bg-ui-bg-subtle">
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Gross Price
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Commission (%)
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Net Cost
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[140px]">
                Net Cost (Manual)
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Margin Rate (%)
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Selling Price
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[140px]">
                Selling Currency
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Exchange Rate
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[160px]">
                Selling Price (Selling Currency)
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-ui-border-base px-2 py-2">
                <Input
                  id="grossPrice"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="100.00"
                  value={pricingData.grossPrice?.toString() || ""}
                  onChange={(e) => {
                    const value = e.target.value
                      ? parseFloat(e.target.value)
                      : undefined;
                    handleInputChange("grossPrice", value);
                  }}
                  disabled={disabled}
                  className="w-full text-sm"
                />
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <Input
                  id="commission"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  placeholder="10"
                  value={
                    pricingData.commission !== undefined
                      ? (pricingData.commission * 100).toString()
                      : ""
                  }
                  onChange={(e) => {
                    const value =
                      e.target.value !== ""
                        ? parseFloat(e.target.value) / 100
                        : undefined;
                    handleInputChange("commission", value);
                  }}
                  disabled={disabled}
                  className="w-full text-sm"
                />
              </td>
              <td className="border border-ui-border-base px-2 py-2 bg-ui-bg-subtle">
                <Text weight="plus" size="small">
                  {(() => {
                    const value =
                      calculations.calculatedSupplierPrice ||
                      pricingData.supplierPrice;
                    if (value == null) return "—";
                    const numValue =
                      typeof value === "string" ? parseFloat(value) : value;
                    return isNaN(numValue) ? "—" : numValue.toFixed(2);
                  })()}
                </Text>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <Input
                  id="supplierPrice"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="90.00"
                  value={pricingData.supplierPrice?.toString() || ""}
                  onChange={(e) => {
                    const value = e.target.value
                      ? parseFloat(e.target.value)
                      : undefined;
                    handleInputChange("supplierPrice", value);
                  }}
                  disabled={
                    disabled ||
                    (pricingData.commission !== undefined &&
                      pricingData.grossPrice !== undefined)
                  }
                  className={`w-full text-sm ${
                    pricingData.commission !== undefined &&
                    pricingData.grossPrice !== undefined
                      ? "bg-ui-bg-subtle cursor-not-allowed"
                      : ""
                  }`}
                />
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <Input
                  id="marginRate"
                  type="number"
                  step="0.01"
                  min="0"
                  max="99.99"
                  placeholder="10"
                  value={
                    pricingData.marginRate !== undefined
                      ? (pricingData.marginRate * 100).toString()
                      : ""
                  }
                  onChange={(e) => {
                    const value =
                      e.target.value !== ""
                        ? parseFloat(e.target.value) / 100
                        : undefined;
                    handleInputChange("marginRate", value);
                  }}
                  disabled={disabled}
                  className="w-full text-sm"
                />
              </td>
              <td className="border border-ui-border-base px-2 py-2 bg-ui-bg-subtle">
                <Text weight="plus" size="small">
                  {(() => {
                    const value = calculations.calculatedSellingPrice;
                    if (value == null) return "—";
                    const numValue =
                      typeof value === "string" ? parseFloat(value) : value;
                    return isNaN(numValue) ? "—" : numValue.toFixed(2);
                  })()}
                </Text>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <Select
                  value={pricingData.sellingCurrency || ""}
                  onValueChange={(value) => {
                    const newValue = value || undefined;
                    setPricingData((prev) => ({
                      ...prev,
                      sellingCurrency: newValue,
                    }));
                  }}
                  disabled={disabled}
                >
                  <Select.Trigger className="w-full text-sm">
                    <Select.Value placeholder="Currency" />
                  </Select.Trigger>
                  <Select.Content>
                    {[
                      "USD",
                      "EUR",
                      "GBP",
                      "JPY",
                      "AUD",
                      "CAD",
                      "CHF",
                      "CNY",
                      "INR",
                    ].map((currency) => (
                      <Select.Item key={currency} value={currency}>
                        {currency}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <Input
                  type="number"
                  step="0.000001"
                  min="0"
                  placeholder="1.0"
                  value={pricingData.exchangeRate?.toString() || ""}
                  onChange={(e) => {
                    const value = e.target.value
                      ? parseFloat(e.target.value)
                      : undefined;
                    handleInputChange("exchangeRate", value);
                  }}
                  disabled={disabled}
                  className="w-full text-sm"
                />
              </td>
              <td className="border border-ui-border-base px-2 py-2 bg-ui-bg-subtle">
                <Text weight="plus" size="small">
                  {(() => {
                    const value =
                      calculations.calculatedSellingPriceSellingCurrency;
                    if (value == null) return "—";
                    const numValue =
                      typeof value === "string" ? parseFloat(value) : value;
                    return isNaN(numValue) ? "—" : numValue.toFixed(2);
                  })()}
                </Text>
              </td>
            </tr>

            {/* Addon Rows - Editable */}
            {addonLineItems.map((addon) => (
              <tr key={addon.id} className="bg-blue-50 dark:bg-blue-900/20">
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-gross-price-${addon.id}`}
                    type="number"
                    value={addon.gross_price?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "gross_price",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="0.00"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.01"
                    min="0"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-commission-${addon.id}`}
                    type="number"
                    value={addon.commission?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "commission",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="0.00"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.01"
                    min="0"
                    max="100"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Text
                    size="small"
                    className="text-blue-700 dark:text-blue-300 px-2 py-1"
                  >
                    {(() => {
                      // Calculate net cost: Gross Price - (Gross Price × Commission)
                      if (addon.gross_price && addon.commission) {
                        const calculatedNetCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                        return calculatedNetCost.toFixed(2);
                      }
                      return addon.net_cost !== undefined
                        ? addon.net_cost.toFixed(2)
                        : "—";
                    })()}
                  </Text>
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-net-cost-manual-${addon.id}`}
                    type="number"
                    value={
                      // Priority: manual value first, then fallback to net_cost if no commission
                      addon.net_cost_manual !== undefined
                        ? addon.net_cost_manual.toString()
                        : addon.commission === undefined &&
                          addon.net_cost !== undefined
                        ? addon.net_cost.toString()
                        : ""
                    }
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "net_cost_manual",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="Manual override"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.01"
                    min="0"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-margin-rate-${addon.id}`}
                    type="number"
                    value={addon.margin_rate?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "margin_rate",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="0.00"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.01"
                    min="0"
                    max="100"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Text
                    size="small"
                    className="text-blue-700 dark:text-blue-300 px-2 py-1"
                  >
                    {(() => {
                      // Calculate selling price: Net Cost ÷ (1 - Margin Rate)
                      let netCost: number | undefined;

                      // Use manual net cost if available, otherwise use calculated net cost
                      if (addon.net_cost_manual !== undefined) {
                        netCost = addon.net_cost_manual;
                      } else if (addon.gross_price && addon.commission) {
                        netCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                      } else {
                        netCost = addon.net_cost;
                      }

                      if (netCost && addon.margin_rate) {
                        const calculatedSellingPrice =
                          netCost / (1 - addon.margin_rate / 100);
                        return calculatedSellingPrice.toFixed(2);
                      }

                      return addon.selling_price !== undefined
                        ? addon.selling_price.toFixed(2)
                        : "—";
                    })()}
                  </Text>
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Select
                    value={addon.selling_currency || costCurrency}
                    onValueChange={(value) =>
                      handleAddonInputChange(
                        addon.id,
                        "selling_currency",
                        value
                      )
                    }
                    disabled={disabled}
                  >
                    <Select.Trigger className="w-full text-sm bg-white">
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="CHF">CHF</Select.Item>
                      <Select.Item value="EUR">EUR</Select.Item>
                      <Select.Item value="USD">USD</Select.Item>
                      <Select.Item value="GBP">GBP</Select.Item>
                    </Select.Content>
                  </Select>
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-exchange-rate-${addon.id}`}
                    type="number"
                    value={addon.exchange_rate?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "exchange_rate",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="1.0000"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.0001"
                    min="0"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Text
                    size="small"
                    className="text-blue-700 dark:text-blue-300 px-2 py-1"
                  >
                    {(() => {
                      // Calculate selling price in selling currency
                      let sellingPrice: number | undefined;
                      let netCost: number | undefined;

                      // Use manual net cost if available, otherwise use calculated net cost
                      if (addon.net_cost_manual !== undefined) {
                        netCost = addon.net_cost_manual;
                      } else if (addon.gross_price && addon.commission) {
                        netCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                      } else {
                        netCost = addon.net_cost;
                      }

                      if (netCost && addon.margin_rate) {
                        sellingPrice = netCost / (1 - addon.margin_rate / 100);
                      } else {
                        sellingPrice = addon.selling_price;
                      }

                      if (sellingPrice && addon.exchange_rate) {
                        const convertedPrice =
                          sellingPrice * addon.exchange_rate;
                        return convertedPrice.toFixed(2);
                      }

                      return addon.selling_price_selling_currency !== undefined
                        ? addon.selling_price_selling_currency.toFixed(2)
                        : sellingPrice !== undefined
                        ? sellingPrice.toFixed(2)
                        : "—";
                    })()}
                  </Text>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Addon Summary Section */}
      {addonLineItems.length > 0 && (
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
          <div className="flex items-center gap-2 mb-3">
            <Text weight="plus" className="text-blue-800 dark:text-blue-200">
              Add-ons Added ({addonLineItems.length})
            </Text>
          </div>
          <div className="space-y-2">
            {addonLineItems.map((addon) => (
              <div
                key={addon.id}
                className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded border border-blue-100 dark:border-blue-800"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <Text className="font-medium text-gray-900 dark:text-gray-100">
                      {addon.name}
                    </Text>
                    {disabled && addon.is_mandatory && (
                      <div className="flex items-center gap-1 px-2 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs rounded-full">
                        <Lock className="h-3 w-3" />
                        <span>Mandatory</span>
                      </div>
                    )}
                    {!disabled && (
                      <div className="flex items-center gap-2">
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={addon.is_mandatory || false}
                            onChange={(e) => {
                              const updatedAddons = addonLineItems.map((item) =>
                                item.id === addon.id
                                  ? { ...item, is_mandatory: e.target.checked }
                                  : item
                              );
                              setAddonLineItems(updatedAddons);
                              onChange?.({
                                ...initialData,
                                addonLineItems: updatedAddons,
                              });
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <Text
                            size="small"
                            className="text-gray-600 dark:text-gray-400"
                          >
                            Mandatory
                          </Text>
                        </label>
                      </div>
                    )}
                  </div>
                  <Text
                    size="small"
                    className="text-gray-600 dark:text-gray-400 mt-1"
                  >
                    {addon.supplier_name
                      ? `Supplier: ${addon.supplier_name}`
                      : ""}
                  </Text>
                </div>
                <div className="text-right">
                  <Text className="font-medium">
                    {(() => {
                      // Calculate current selling price (same logic as in table)
                      let netCost: number | undefined;

                      if (addon.net_cost_manual !== undefined) {
                        netCost = addon.net_cost_manual;
                      } else if (addon.gross_price && addon.commission) {
                        netCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                      } else {
                        netCost = addon.net_cost;
                      }

                      if (netCost && addon.margin_rate) {
                        const calculatedSellingPrice =
                          netCost / (1 - addon.margin_rate / 100);
                        return `${calculatedSellingPrice.toFixed(2)} ${
                          addon.selling_currency || costCurrency
                        }`;
                      }

                      return addon.selling_price !== undefined
                        ? `${addon.selling_price.toFixed(2)} ${
                            addon.selling_currency || costCurrency
                          }`
                        : "—";
                    })()}
                  </Text>
                  {(() => {
                    let netCost: number | undefined;

                    if (addon.net_cost_manual !== undefined) {
                      netCost = addon.net_cost_manual;
                    } else if (addon.gross_price && addon.commission) {
                      netCost =
                        addon.gross_price -
                        addon.gross_price * (addon.commission / 100);
                    } else {
                      netCost = addon.net_cost;
                    }

                    return (
                      netCost !== undefined && (
                        <Text
                          size="small"
                          className="text-gray-600 dark:text-gray-400"
                        >
                          Net Cost: {netCost.toFixed(2)}{" "}
                          {addon.currency || costCurrency}
                        </Text>
                      )
                    );
                  })()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Errors */}
      {errors.length > 0 && (
        <div className="space-y-2">
          <Text weight="plus" className="text-ui-fg-error">
            Pricing Errors:
          </Text>
          {errors.map((error, index) => (
            <Text key={index} size="small" className="text-ui-fg-error">
              • {error}
            </Text>
          ))}
        </div>
      )}
    </div>
  );
};

export default PricingCalculator;
