import React, { useState, useEffect } from "react";
import { DatePicker, Text, toast, Popover, Button } from "@camped-ai/ui";
import { Calendar, Loader2 } from "lucide-react";
import { CircularProgress } from "@mui/material";

interface PopoverEditValidityCellProps {
  activeFrom: Date | string | null;
  activeTo: Date | string | null;
  onSave: (data: {
    active_from?: string | null;
    active_to?: string | null;
  }) => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

const PopoverEditValidityCell: React.FC<PopoverEditValidityCellProps> = ({
  activeFrom,
  activeTo,
  onSave,
  isLoading = false,
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [editFromDate, setEditFromDate] = useState<Date | null>(
    activeFrom ? new Date(activeFrom) : null
  );
  const [editToDate, setEditToDate] = useState<Date | null>(
    activeTo ? new Date(activeTo) : null
  );
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    setEditFromDate(activeFrom ? new Date(activeFrom) : null);
    setEditToDate(activeTo ? new Date(activeTo) : null);
  }, [activeFrom, activeTo]);

  const formatValidityPeriod = (
    from: Date | string | null,
    to: Date | string | null
  ) => {
    // Debug: Log the validity period values
    console.log("🔍 Validity Debug - Input values:", {
      from: from,
      to: to,
      from_type: typeof from,
      to_type: typeof to
    });

    if (!from && !to) return "Not set";

    const formatDate = (dateStr: string) => {
      const date = new Date(dateStr);
      // Use consistent DD/MM/YYYY format to match Excel import expectations
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const formatted = `${day}/${month}/${year}`;

      console.log("🔍 Validity Debug - Date formatting:", {
        input: dateStr,
        parsed_date: date,
        formatted: formatted,
        iso_string: date.toISOString()
      });
      return formatted;
    };

    if (from && to) {
      return `${formatDate(from.toString())} → ${formatDate(to.toString())}`;
    } else if (from) {
      return `${formatDate(from.toString())} → Open`;
    } else if (to) {
      return `Until ${formatDate(to.toString())}`;
    }

    return "Not set";
  };

  const handleSave = async () => {
    // Check if values have changed
    const originalFrom = activeFrom ? new Date(activeFrom) : null;
    const originalTo = activeTo ? new Date(activeTo) : null;

    // Properly compare dates, handling null values
    const fromChanged =
      (originalFrom === null && editFromDate !== null) ||
      (originalFrom !== null && editFromDate === null) ||
      (originalFrom !== null &&
        editFromDate !== null &&
        originalFrom.getTime() !== editFromDate.getTime());

    const toChanged =
      (originalTo === null && editToDate !== null) ||
      (originalTo !== null && editToDate === null) ||
      (originalTo !== null &&
        editToDate !== null &&
        originalTo.getTime() !== editToDate.getTime());

    if (!fromChanged && !toChanged) {
      setIsOpen(false);
      return;
    }

    // Validate date range (use >= to match edit page logic)
    if (editFromDate && editToDate && editFromDate >= editToDate) {
      toast.error("Active To date must be after Active From date");
      return;
    }

    setIsSaving(true);
    try {
      const updateData: {
        active_from?: string | null;
        active_to?: string | null;
      } = {};

      if (fromChanged) {
        if (editFromDate) {
          // Format date as YYYY-MM-DD in local timezone to avoid timezone issues
          const year = editFromDate.getFullYear();
          const month = String(editFromDate.getMonth() + 1).padStart(2, "0");
          const day = String(editFromDate.getDate()).padStart(2, "0");
          updateData.active_from = `${year}-${month}-${day}`;
        } else {
          updateData.active_from = null;
        }
      }

      if (toChanged) {
        if (editToDate) {
          // Format date as YYYY-MM-DD in local timezone to avoid timezone issues
          const year = editToDate.getFullYear();
          const month = String(editToDate.getMonth() + 1).padStart(2, "0");
          const day = String(editToDate.getDate()).padStart(2, "0");
          updateData.active_to = `${year}-${month}-${day}`;
        } else {
          // Send null to explicitly set an open-ended period
          updateData.active_to = null;
        }
      }

      await onSave(updateData);
      setIsOpen(false);
      toast.success("Validity period updated successfully");
    } catch (error: any) {
      // Handle specific error types for better user experience
      let errorMessage = "Failed to update validity period";

      if (error?.response?.status === 409) {
        // Handle validation errors from the server
        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        // Provide specific guidance for different types of conflicts
        if (errorMessage.includes("Date range conflict")) {
          // Already has specific date information
        } else if (errorMessage.includes("open-ended validity")) {
          // Already has specific open-ended conflict information
        } else if (errorMessage.includes("Cannot create offering")) {
          // Already has specific creation conflict information
        } else if (
          errorMessage.includes("Both offerings would be open-ended")
        ) {
          // Already has specific open-ended conflict information
        } else if (errorMessage.includes("Cannot set open-ended validity")) {
          // Already has specific open-ended conflict information
        } else if (
          errorMessage.includes(
            "Cannot overlap with existing open-ended offering"
          )
        ) {
          // Already has specific open-ended conflict information
        } else {
          // Generic conflict message
          errorMessage = "This supplier offering configuration already exists.";
        }
      } else if (error?.response?.status === 400) {
        errorMessage = error.message || "Invalid date range provided";
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
      // Reset to original values
      setEditFromDate(activeFrom ? new Date(activeFrom) : null);
      setEditToDate(activeTo ? new Date(activeTo) : null);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditFromDate(activeFrom ? new Date(activeFrom) : null);
    setEditToDate(activeTo ? new Date(activeTo) : null);
    setIsOpen(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>
        <div
          className={`cursor-pointer hover:bg-ui-bg-subtle rounded px-2 py-1 transition-colors border border-transparent hover:border-ui-border-base ${
            isLoading ? "opacity-50" : ""
          } ${className}`}
          title={isLoading ? "Updating..." : "Click to edit"}
        >
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3 text-ui-fg-subtle" />
            <Text size="small">
              {formatValidityPeriod(activeFrom, activeTo)}
            </Text>
            {isLoading && <CircularProgress size={12} />}
          </div>
          <div className="text-xs text-ui-fg-subtle mt-1">
            {isLoading ? "Updating..." : "Click to edit"}
          </div>
        </div>
      </Popover.Trigger>
      <Popover.Content side="top" align="start" className="w-[300px] p-4">
        <div className="space-y-4">
          <div>
            <Text size="small" weight="plus" className="mb-1">
              Valid From
            </Text>
            <DatePicker
              value={editFromDate}
              onChange={setEditFromDate}
              isDisabled={isSaving}
            />
          </div>

          <div>
            <Text size="small" weight="plus" className="mb-1">
              Valid To
              <span className="text-xs text-ui-fg-subtle ml-1">
                (Leave empty for open-ended)
              </span>
            </Text>
            <DatePicker
              value={editToDate}
              onChange={setEditToDate}
              isDisabled={isSaving}
            />
            <div className="flex items-center gap-2 mt-1">
              <button
                type="button"
                onClick={() => setEditToDate(null)}
                disabled={isSaving}
                className="text-xs text-ui-fg-subtle hover:text-ui-fg-base underline"
              >
                Clear (make open-ended)
              </button>
              {!editToDate && (
                <Text size="small" className="text-ui-fg-subtle">
                  This offering will have no end date
                </Text>
              )}
            </div>
          </div>

          <div className="flex items-center justify-end gap-2 pt-2 border-t">
            <Button
              type="button"
              variant="secondary"
              size="small"
              onClick={handleCancel}
              disabled={isSaving}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="primary"
              size="small"
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
              ) : null}
              Save
            </Button>
          </div>
        </div>
      </Popover.Content>
    </Popover>
  );
};

export default PopoverEditValidityCell;
