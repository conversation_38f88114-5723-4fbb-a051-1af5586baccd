import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft, Trash } from "@camped-ai/icons";
import { Edit, Package } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  IconButton,
  Tabs,
  Prompt,
} from "@camped-ai/ui";
import { useNavigate, useParams } from "react-router-dom";
import { useState } from "react";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import {
  useProductService,
  useDeleteProductService,
} from "../../../../hooks/supplier-products-services/use-products-services";

// Tab components
import SupplierProductServiceTabs from "../../../../components/supplier-management/supplier-product-service-tabs";
import OverviewTab from "../../../../components/supplier-management/tabs/overview-tab";
import CostHistoryTab from "../../../../components/supplier-management/tabs/cost-history-tab";
import { useSupplierProductServiceTabState } from "../../../../hooks/useSupplierProductServiceTabState";
import "./name-styles.css";

const ProductServiceDetailPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [showFullName, setShowFullName] = useState(false);

  // Tab state management
  const { activeTab, setActiveTab } = useSupplierProductServiceTabState({
    defaultTab: "overview",
  });

  // Use real API hooks
  const { data: productServiceData, isLoading, error } = useProductService(id!);
  const deleteProductService = useDeleteProductService();

  const productService = productServiceData?.product_service;

  // Helper function to determine if name is long (more than 100 characters)
  const isLongName = productService?.name && productService.name.length > 200;

  // Handle error state
  if (error) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="max-w-4xl mx-auto">
          <div className="flex items-center gap-4 mb-6">
            <Button
              variant="secondary"
              size="small"
              onClick={() => navigate("/supplier-management/products-services")}
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <Heading level="h2">Product/Service Not Found</Heading>
              <Text className="text-ui-fg-subtle">
                The requested product or service could not be found.
              </Text>
            </div>
          </div>
        </Container>
        <Toaster />
      </>
    );
  }

  // Handle loading state
  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />

        {/* Header with back button and actions skeleton */}
        <div className="flex flex-row items-center justify-between mb-2">
          <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
          <div className="flex items-center gap-2">
            <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
            <div className="h-8 w-20 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>

        <Container>
          {/* Product/Service header skeleton */}
          <div className="flex flex-row items-center text-wrap mb-5 ">
            <div className="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-lg animate-pulse" />
            <div className="h-6 w-48 bg-gray-200 rounded animate-pulse ml-3" />
          </div>

          {/* Tab navigation skeleton */}
          <div className="my-6">
            <div className="flex space-x-8">
              <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>

          {/* Tab content skeleton */}
          <div className="space-y-6">
            <div className="h-32 bg-gray-200 rounded animate-pulse" />
            <div className="h-32 bg-gray-200 rounded animate-pulse" />
            <div className="h-32 bg-gray-200 rounded animate-pulse" />
            <div className="h-32 bg-gray-200 rounded animate-pulse" />
          </div>
        </Container>

        <Toaster />
      </>
    );
  }

  if (!productService) {
    return null;
  }

  const handleDelete = async () => {
    try {
      await deleteProductService.mutateAsync(productService.id);
      setIsDeleteModalOpen(false);
      navigate("/supplier-management/products-services");
    } catch (error) {
      // Error is handled by the mutation hook with proper toast messages
      console.error("Error deleting product/service:", error);
      setIsDeleteModalOpen(false);
    }
  };

  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  return (
    <>
      <PermissionBasedSidebarHider />

      <div className="flex flex-row items-center justify-between">
        <IconButton
          onClick={() => navigate("/supplier-management/products-services")}
        >
          <ArrowLeft className="w-4 h-4" />
        </IconButton>

        <div className="flex items-center gap-2">
          <Button
            variant="secondary"
            onClick={() =>
              navigate(
                `/supplier-management/products-services/${productService.id}/edit`
              )
            }
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Prompt open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
            <Prompt.Trigger asChild>
              <Button variant="danger">
                <Trash className="h-4 w-4" />
                Delete
              </Button>
            </Prompt.Trigger>
            <Prompt.Content>
              <Prompt.Header>
                <Prompt.Title>Delete Product/Service</Prompt.Title>
                <Prompt.Description>
                  Are you sure you want to delete "{productService.name}"? This
                  action cannot be undone.
                </Prompt.Description>
              </Prompt.Header>
              <Prompt.Footer>
                <Prompt.Cancel onClick={handleCancelDelete}>
                  Cancel
                </Prompt.Cancel>
                <Prompt.Action onClick={handleDelete}>Delete</Prompt.Action>
              </Prompt.Footer>
            </Prompt.Content>
          </Prompt>
        </div>
      </div>
      <Container>
        <div className="flex flex-col mb-5">
          <div className="flex flex-row items-center text-wrap">
            <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <Package className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1 pl-3">
              <Heading
                level="h2"
                className={`text-xl font-semibold ${
                  !showFullName && isLongName ? 'product-name-clamp-2' : ''
                }`}
                style={{
                  wordBreak: 'break-word'
                }}
              >
                {productService.name}
              </Heading>
              {isLongName && (
                <div className="mt-2">
                  <Button
                    variant="transparent"
                    size="small"
                    onClick={() => setShowFullName(!showFullName)}
                    className="text-blue-600 hover:text-blue-700 p-0 h-auto font-normal text-sm"
                  >
                    {showFullName ? "Show Less" : "Show More"}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Tab Navigation and Content */}
        <SupplierProductServiceTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="w-full"
        >
          <Tabs.Content value="overview">
            <OverviewTab productService={productService} />
          </Tabs.Content>

          <Tabs.Content value="cost-history">
            <CostHistoryTab
              productServiceId={id!}
              productServiceName={productService.name}
            />
          </Tabs.Content>
        </SupplierProductServiceTabs>
      </Container>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Product/Service Details",
});

export default ProductServiceDetailPage;
